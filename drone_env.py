
import numpy as np

class DroneEnv:
    def __init__(self):
        self.FIELD_SIZE = 100  # meters
        self.DRONE_SPEED = 15  # m/s (fixed speed)
        self.STEP_SIZE = 1  # meters per step
        self.USER_MAX_SPEED = 3  # m/s
        self.TARGET_REACH_THRESHOLD = 1  # meters
        self.STEPS_PER_USER_MOVE = 15  # 无人机执行15步后用户位置变化

        self.drone_pos = np.array([10.0, 10.0])
        self.user_pos = np.array([60.0, 60.0])
        self.step_count = 0  # 记录步数，用于控制用户移动时机

        # 状态空间：无人机位置(2) + 目标相对位置(2) + 相对距离(1) = 5维
        self.state_dim = 5
        # 动作空间：飞行方向，8个方向 + 停止 = 9个动作
        self.action_dim = 9  # 0-7: 8个方向, 8: 停止

        # 定义8个方向的单位向量
        self.directions = np.array([
            [1, 0],    # 东
            [1, 1],    # 东北
            [0, 1],    # 北
            [-1, 1],   # 西北
            [-1, 0],   # 西
            [-1, -1],  # 西南
            [0, -1],   # 南
            [1, -1],   # 东南
            [0, 0]     # 停止
        ])
        # 归一化对角线方向
        for i in range(8):
            if np.linalg.norm(self.directions[i]) > 0:
                self.directions[i] = self.directions[i] / np.linalg.norm(self.directions[i])

    def reset(self):
        self.drone_pos = np.array([10.0, 10.0])
        self.user_pos = np.array([60.0, 60.0])
        self.step_count = 0
        return self._get_state()

    def _get_state(self):
        # 状态空间：无人机位置 + 目标相对于无人机的位置 + 无人机与目标的相对距离
        relative_pos = self.user_pos - self.drone_pos  # 目标相对于无人机的位置
        distance = np.linalg.norm(relative_pos)  # 相对距离

        state = np.concatenate([
            self.drone_pos,      # 无人机位置 (2维)
            relative_pos,        # 目标相对位置 (2维)
            [distance]           # 相对距离 (1维)
        ])
        return state

    def step(self, action):
        # 确保动作在有效范围内
        action = int(np.clip(action, 0, self.action_dim - 1))

        # 无人机移动：根据动作选择方向，移动1米
        direction = self.directions[action]
        new_drone_pos = self.drone_pos + direction * self.STEP_SIZE

        # 检查无人机是否超出边界
        if (new_drone_pos[0] < 0 or new_drone_pos[0] > self.FIELD_SIZE or
            new_drone_pos[1] < 0 or new_drone_pos[1] > self.FIELD_SIZE):
            # 超出边界，给予惩罚并结束训练
            reward = -100  # 大额惩罚
            done = True
            return self._get_state(), reward, done, {"boundary_violation": True}

        self.drone_pos = new_drone_pos
        self.step_count += 1

        # 每15步后用户位置变化一次
        if self.step_count % self.STEPS_PER_USER_MOVE == 0:
            # 用户移动（随机游走，有边界约束）
            user_move = (np.random.rand(2) - 0.5) * 2 * self.USER_MAX_SPEED
            new_user_pos = self.user_pos + user_move

            # 用户边界约束
            new_user_pos = np.clip(new_user_pos, 0, self.FIELD_SIZE)
            self.user_pos = new_user_pos

        # 计算当前距离
        distance = np.linalg.norm(self.drone_pos - self.user_pos)

        # 奖励函数设计
        reward = 0

        # 1. 每一步都有小的负奖励，鼓励最短路径
        reward -= 0.1

        # 2. 随着无人机接近目标而增加奖励
        # 使用距离的倒数作为接近奖励，距离越近奖励越大
        if distance > 0:
            approach_reward = 10.0 / (distance + 1)  # +1避免除零
            reward += approach_reward

        # 3. 当无人机距离目标1米以内时获得大额奖励
        done = False
        if distance < self.TARGET_REACH_THRESHOLD:
            reward += 100  # 大额奖励
            done = True

        return self._get_state(), reward, done, {"distance": distance}

    def render(self):
        # This will be implemented later for visualization
        pass

    def close(self):
        pass


