
import numpy as np

class DroneEnv:
    def __init__(self):
        self.FIELD_SIZE = 100  # meters
        self.DRONE_MAX_SPEED = 20  # m/s
        self.USER_MAX_SPEED = 3  # m/s
        self.TARGET_REACH_THRESHOLD = 1  # meters

        self.drone_pos = np.array([10.0, 10.0])
        self.user_pos = np.array([60.0, 60.0])

        self.state_dim = 8  # drone_x, drone_y, user_x, user_y, dist_x, dist_y, drone_speed_x, drone_speed_y
        self.action_dim = 2  # drone_speed_x, drone_speed_y

    def reset(self):
        self.drone_pos = np.array([10.0, 10.0])
        self.user_pos = np.array([60.0, 60.0])
        return self._get_state()

    def _get_state(self):
        dist_to_user = self.user_pos - self.drone_pos
        # For simplicity, let's assume drone speed is part of the state for now.
        # In a real scenario, you might track previous actions or use a more complex state.
        drone_speed = np.array([0.0, 0.0]) # Placeholder, will be updated with actual speed in step
        return np.concatenate([self.drone_pos, self.user_pos, dist_to_user, drone_speed])

    def step(self, action):
        # Drone movement
        action = np.clip(action, -self.DRONE_MAX_SPEED, self.DRONE_MAX_SPEED)
        self.drone_pos += action  # action is directly interpreted as velocity vector

        # User movement (random walk with boundary constraints)
        user_move = (np.random.rand(2) - 0.5) * 2 * self.USER_MAX_SPEED
        new_user_pos = self.user_pos + user_move
        
        # Boundary constraints for user
        new_user_pos = np.clip(new_user_pos, 0, self.FIELD_SIZE)
        self.user_pos = new_user_pos

        # Reward calculation
        distance = np.linalg.norm(self.drone_pos - self.user_pos)
        reward = -distance  # Negative distance as reward (closer is better)

        done = distance < self.TARGET_REACH_THRESHOLD

        # Update drone speed in state for next observation
        drone_speed = action # current action is the speed for this step
        state = np.concatenate([self.drone_pos, self.user_pos, self.user_pos - self.drone_pos, drone_speed])

        return state, reward, done, {}

    def render(self):
        # This will be implemented later for visualization
        pass

    def close(self):
        pass


