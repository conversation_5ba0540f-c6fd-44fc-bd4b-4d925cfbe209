
import torch
import numpy as np
from drone_env import DroneEnv
from ddpg_agent import DDPG
import matplotlib.pyplot as plt
import os

def main():
    env = DroneEnv()
    state_dim = env.state_dim
    action_dim = env.action_dim
    max_action = env.DRONE_MAX_SPEED

    agent = DDPG(state_dim, action_dim, max_action)

    # Load pre-trained model if exists
    if os.path.exists("ddpg_actor.pth"):
        agent.actor.load_state_dict(torch.load("ddpg_actor.pth"))
        agent.actor_target.load_state_dict(torch.load("ddpg_actor_target.pth"))
        agent.critic.load_state_dict(torch.load("ddpg_critic.pth"))
        agent.critic_target.load_state_dict(torch.load("ddpg_critic_target.pth"))
        print("Loaded pre-trained DDPG model.")

    num_episodes = 50  # Reduced for testing
    max_steps_per_episode = 500
    
    rewards_per_episode = []
    success_rates = []
    avg_flight_times = []

    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        episode_flight_time = 0
        done = False
        
        drone_trajectory = [env.drone_pos.copy()]
        user_trajectory = [env.user_pos.copy()]

        for step in range(max_steps_per_episode):
            action = agent.select_action(state)
            
            # Add noise to action for exploration
            action = (action + np.random.normal(0, 0.1, size=action_dim)).clip(-max_action, max_action)

            next_state, reward, done, _ = env.step(action)
            agent.replay_buffer.push(state, action, reward, next_state, done)
            
            state = next_state
            episode_reward += reward
            episode_flight_time += 1
            
            drone_trajectory.append(env.drone_pos.copy())
            user_trajectory.append(env.user_pos.copy())

            agent.update()

            if done:
                break
        
        rewards_per_episode.append(episode_reward)
        success_rates.append(1 if done else 0)
        avg_flight_times.append(episode_flight_time)

        print(f"Episode: {episode+1}, Reward: {episode_reward:.2f}, Success: {done}, Flight Time: {episode_flight_time}")

        # Save trajectories for visualization (e.g., every 10 episodes)
        if (episode + 1) % 10 == 0:
            np.save(f'drone_trajectory_episode_{episode+1}.npy', np.array(drone_trajectory))
            np.save(f'user_trajectory_episode_{episode+1}.npy', np.array(user_trajectory))
            # Save agent models periodically
            torch.save(agent.actor.state_dict(), 'ddpg_actor.pth')
            torch.save(agent.actor_target.state_dict(), 'ddpg_actor_target.pth')
            torch.save(agent.critic.state_dict(), 'ddpg_critic.pth')
            torch.save(agent.critic_target.state_dict(), 'ddpg_critic_target.pth')
            print(f"Saved DDPG model at episode {episode+1}")

    # Save performance metrics
    np.save('rewards_per_episode.npy', np.array(rewards_per_episode))
    np.save('success_rates.npy', np.array(success_rates))
    np.save('avg_flight_times.npy', np.array(avg_flight_times))

    print("Training finished.")

if __name__ == '__main__':
    main()


