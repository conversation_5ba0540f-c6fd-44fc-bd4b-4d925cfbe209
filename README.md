# 无人机动态目标跟踪系统

## 项目概述

本项目旨在利用PyTorch和深度确定性策略梯度（DDPG）强化学习算法，解决无人机在100m x 100m模拟环境中动态跟踪移动目标的问题。无人机需要学习如何高效地导航并接近不断移动的目标，同时优化飞行路径。

## 技术栈

- **Python**: 核心编程语言
- **PyTorch**: 深度学习框架，用于实现DDPG算法
- **NumPy**: 用于数值计算和数据处理
- **Matplotlib**: 用于数据可视化和轨迹绘制

## 环境设置

模拟环境是一个100m x 100m的方形区域。无人机和目标都从预设位置开始移动。

- **无人机最大速度**: 20 m/s
- **目标最大速度**: 3 m/s，并具有随机移动模式
- **成功条件**: 无人机距离目标1米以内

## DDPG算法

DDPG是一种基于Actor-Critic架构的强化学习算法，适用于连续动作空间。在本项目中，DDPG用于训练无人机代理，使其能够根据当前状态（无人机位置、目标位置、相对距离和速度）输出连续的动作（无人机速度向量）。

### 状态空间

- 无人机位置 (x, y)
- 目标位置 (x, y)
- 无人机与目标的相对距离
- 无人机与目标的相对速度

### 动作空间

- 无人机在x和y方向上的速度分量，范围在[-DRONE_MAX_SPEED, DRONE_MAX_SPEED]之间。

### 奖励函数

奖励函数旨在鼓励无人机高效地接近目标并保持在环境中。主要组成部分包括：

- **接近奖励**: 随着无人机接近目标而增加。
- **成功奖励**: 当无人机距离目标1米以内时获得大额奖励。
- **时间惩罚**: 每一步都会有小的负奖励，鼓励最短路径。
- **边界惩罚**: 如果目标无人机超出环境边界，则施加惩罚，同时训练结束。

## 文件结构

- `requirements.txt`: 项目依赖库
- `drone_env.py`: 仿真环境的实现，包括无人机和目标的运动动力学、状态空间、动作空间和奖励函数。
- `ddpg_agent.py`: DDPG算法的核心组件，包括Actor和Critic网络、经验回放缓冲区和DDPG代理的训练逻辑。
- `main.py`: 主训练脚本，负责初始化环境和代理，运行训练循环，并保存训练过程中的性能指标和轨迹数据。
- `visualize.py`: 可视化脚本，用于绘制训练过程中的奖励、成功率、平均飞行时间以及无人机和目标的轨迹。

## 运行指南

1. **安装依赖**: 
   ```bash
   pip install -r requirements.txt
   ```

2. **训练DDPG代理**: 
   运行`main.py`脚本开始训练。训练过程可能需要一些时间，具体取决于`num_episodes`的设置。
   ```bash
   python3 main.py
   ```
   训练过程中，模型权重和轨迹数据会定期保存。

3. **可视化结果**: 
   训练完成后，运行`visualize.py`脚本生成性能指标图和无人机/目标轨迹图。
   ```bash
   python3 visualize.py
   ```
   生成的图像文件将保存在项目根目录下。

## 性能指标

- **成功率**: 无人机成功到达目标位置的剧集比例。
- **平均飞行时间**: 每次成功跟踪目标所需的平均步数。
- **奖励曲线**: 训练过程中每集获得的累积奖励变化趋势。

## 结果展示

训练完成后，您将在项目目录下找到以下文件：

- `performance_metrics.png`: 包含奖励、成功率和平均飞行时间的图表。
- `trajectory_episode_XX.png`: 多个轨迹图，展示了无人机和目标在不同训练阶段的移动路径。

## 结论

本项目成功实现了基于PyTorch和DDPG的无人机动态目标跟踪系统。通过强化学习，无人机能够学习到有效的导航策略，以适应目标的动态移动，并在模拟环境中实现高效的目标跟踪。

