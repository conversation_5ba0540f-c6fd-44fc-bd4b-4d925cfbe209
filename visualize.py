
import numpy as np
import matplotlib.pyplot as plt
import os

def plot_performance_metrics():
    rewards = np.load("rewards_per_episode.npy")
    success_rates = np.load("success_rates.npy")
    avg_flight_times = np.load("avg_flight_times.npy")

    episodes = np.arange(len(rewards)) + 1

    plt.figure(figsize=(18, 5))

    plt.subplot(1, 3, 1)
    plt.plot(episodes, rewards)
    plt.title("Reward per Episode")
    plt.xlabel("Episode")
    plt.ylabel("Reward")
    plt.grid(True)

    plt.subplot(1, 3, 2)
    plt.plot(episodes, success_rates)
    plt.title("Success Rate per Episode")
    plt.xlabel("Episode")
    plt.ylabel("Success Rate")
    plt.grid(True)

    plt.subplot(1, 3, 3)
    plt.plot(episodes, avg_flight_times)
    plt.title("Average Flight Time per Episode")
    plt.xlabel("Episode")
    plt.ylabel("Flight Time")
    plt.grid(True)

    plt.tight_layout()
    plt.savefig("performance_metrics.png")
    plt.close()
    print("Performance metrics plot saved to performance_metrics.png")

def plot_trajectories():
    # Episodes for which trajectories were saved (multiples of 10 up to 50)
    saved_episodes = [i for i in range(10, 51, 10)]

    for episode_num in saved_episodes:
        drone_traj_file = f'drone_trajectory_episode_{episode_num}.npy'
        user_traj_file = f'user_trajectory_episode_{episode_num}.npy'

        if os.path.exists(drone_traj_file) and os.path.exists(user_traj_file):
            drone_trajectory = np.load(drone_traj_file)
            user_trajectory = np.load(user_traj_file)

            plt.figure(figsize=(8, 8))
            plt.plot(drone_trajectory[:, 0], drone_trajectory[:, 1], label='Drone Trajectory', color='blue')
            plt.plot(user_trajectory[:, 0], user_trajectory[:, 1], label='User Trajectory', color='red', linestyle='--')
            plt.scatter(drone_trajectory[0, 0], drone_trajectory[0, 1], color='green', marker='o', s=100, label='Drone Start')
            plt.scatter(user_trajectory[0, 0], user_trajectory[0, 1], color='purple', marker='x', s=100, label='User Start')
            plt.scatter(drone_trajectory[-1, 0], drone_trajectory[-1, 1], color='cyan', marker='*', s=100, label='Drone End')
            plt.scatter(user_trajectory[-1, 0], user_trajectory[-1, 1], color='orange', marker='+', s=100, label='User End')

            plt.title(f'Drone and User Trajectories - Episode {episode_num}')
            plt.xlabel('X-coordinate (m)')
            plt.ylabel('Y-coordinate (m)')
            plt.xlim(0, 100)
            plt.ylim(0, 100)
            plt.legend()
            plt.grid(True)
            plt.gca().set_aspect('equal', adjustable='box')
            plt.savefig(f'trajectory_episode_{episode_num}.png')
            plt.close()
            print(f"Trajectory plot for episode {episode_num} saved to trajectory_episode_{episode_num}.png")
        else:
            print(f"Trajectory files for episode {episode_num} not found. Skipping.")

if __name__ == '__main__':
    plot_performance_metrics()
    plot_trajectories()


